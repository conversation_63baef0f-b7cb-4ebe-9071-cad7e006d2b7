/**
 * Individual Payment API endpoint
 * Handles operations on specific payments (GET, PUT)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields, isValidUUID } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logPaymentOperation, getRequestContext } from '@/lib/activity-logger';
import { PaymentType, PaymentMethod, PaymentStatus, ActivityAction } from '@/types';
import { PAYMENT_CONFIG } from '../../../../../shared/utils/constants';

interface Payment {
  id: string;
  student_id: string;
  amount: number;
  payment_type: PaymentType;
  payment_method: PaymentMethod;
  description?: string;
  status: PaymentStatus;
  processed_by: string;
  payment_date: Date;
  created_at: Date;
  updated_at: Date;
}

interface PaymentWithUser extends Payment {
  processed_by_name?: string;
  processed_by_email?: string;
}

interface UpdatePaymentRequest {
  amount?: number;
  paymentType?: PaymentType;
  paymentMethod?: PaymentMethod;
  description?: string;
  status?: PaymentStatus;
  paymentDate?: string;
}

// GET /api/payments/[id] - Get payment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'payments', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid payment ID format', 400);
    }

    // Get payment with user information
    const sql = `
      SELECT 
        p.id,
        p.student_id,
        p.amount,
        p.payment_type,
        p.payment_method,
        p.description,
        p.status,
        p.processed_by,
        p.payment_date,
        p.created_at,
        p.updated_at,
        u.name as processed_by_name,
        u.email as processed_by_email
      FROM payments p
      LEFT JOIN users u ON p.processed_by = u.id
      WHERE p.id = $1
    `;

    const result = await query<PaymentWithUser>(sql, [id]);

    if (result.rows.length === 0) {
      return createErrorResponse('Payment not found', 404);
    }

    const payment = result.rows[0];

    // Format response
    const responsePayment = {
      id: payment.id,
      studentId: payment.student_id,
      amount: payment.amount,
      paymentType: payment.payment_type,
      paymentMethod: payment.payment_method,
      description: payment.description,
      status: payment.status,
      processedBy: payment.processed_by,
      paymentDate: payment.payment_date,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at,
      processedByUser: payment.processed_by_name ? {
        id: payment.processed_by,
        name: payment.processed_by_name,
        email: payment.processed_by_email
      } : undefined
    };

    return createResponse(responsePayment, true, 'Payment retrieved successfully');

  } catch (error) {
    console.error('Error fetching payment:', error);
    return createErrorResponse('Failed to fetch payment', 500);
  }
}

// PUT /api/payments/[id] - Update payment
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'payments', 'update')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid payment ID format', 400);
    }

    const body: UpdatePaymentRequest = await request.json();

    // Get current payment data
    const currentSql = `
      SELECT id, student_id, amount, payment_type, payment_method, 
             description, status, processed_by, payment_date, created_at, updated_at
      FROM payments 
      WHERE id = $1
    `;

    const currentResult = await query<Payment>(currentSql, [id]);

    if (currentResult.rows.length === 0) {
      return createErrorResponse('Payment not found', 404);
    }

    const currentPayment = currentResult.rows[0];

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateParams: any[] = [];
    let paramIndex = 1;

    if (body.amount !== undefined) {
      if (body.amount < PAYMENT_CONFIG.MIN_AMOUNT || body.amount > PAYMENT_CONFIG.MAX_AMOUNT) {
        return createErrorResponse(
          `Amount must be between $${PAYMENT_CONFIG.MIN_AMOUNT} and $${PAYMENT_CONFIG.MAX_AMOUNT}`,
          400
        );
      }
      updateFields.push(`amount = $${paramIndex}`);
      updateParams.push(body.amount);
      paramIndex++;
    }

    if (body.paymentType !== undefined) {
      if (!Object.values(PaymentType).includes(body.paymentType)) {
        return createErrorResponse('Invalid payment type', 400);
      }
      updateFields.push(`payment_type = $${paramIndex}`);
      updateParams.push(body.paymentType);
      paramIndex++;
    }

    if (body.paymentMethod !== undefined) {
      if (!Object.values(PaymentMethod).includes(body.paymentMethod)) {
        return createErrorResponse('Invalid payment method', 400);
      }
      updateFields.push(`payment_method = $${paramIndex}`);
      updateParams.push(body.paymentMethod);
      paramIndex++;
    }

    if (body.description !== undefined) {
      updateFields.push(`description = $${paramIndex}`);
      updateParams.push(body.description || null);
      paramIndex++;
    }

    if (body.status !== undefined) {
      if (!Object.values(PaymentStatus).includes(body.status)) {
        return createErrorResponse('Invalid payment status', 400);
      }
      updateFields.push(`status = $${paramIndex}`);
      updateParams.push(body.status);
      paramIndex++;
    }

    if (body.paymentDate !== undefined) {
      const processedPaymentDate = new Date(body.paymentDate);
      if (isNaN(processedPaymentDate.getTime())) {
        return createErrorResponse('Invalid payment date format', 400);
      }
      updateFields.push(`payment_date = $${paramIndex}`);
      updateParams.push(processedPaymentDate);
      paramIndex++;
    }

    // If no fields to update
    if (updateFields.length === 0) {
      return createErrorResponse('No fields to update', 400);
    }

    // Add updated_at field
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

    // Add WHERE clause parameter
    updateParams.push(id);

    // Execute update
    const updateSql = `
      UPDATE payments 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, student_id, amount, payment_type, payment_method, 
                description, status, processed_by, payment_date, created_at, updated_at
    `;

    const updateResult = await query<Payment>(updateSql, updateParams);
    const updatedPayment = updateResult.rows[0];

    // Log the payment update
    const context = getRequestContext(request.headers);
    await logPaymentOperation(
      ActivityAction.UPDATE,
      authResult.user.id,
      {
        id: updatedPayment.id,
        studentId: updatedPayment.student_id,
        amount: updatedPayment.amount,
        paymentType: updatedPayment.payment_type,
        paymentMethod: updatedPayment.payment_method,
        description: updatedPayment.description,
        status: updatedPayment.status
      },
      {
        id: currentPayment.id,
        studentId: currentPayment.student_id,
        amount: currentPayment.amount,
        paymentType: currentPayment.payment_type,
        paymentMethod: currentPayment.payment_method,
        description: currentPayment.description,
        status: currentPayment.status
      },
      context
    );

    // Format response
    const responsePayment = {
      id: updatedPayment.id,
      studentId: updatedPayment.student_id,
      amount: updatedPayment.amount,
      paymentType: updatedPayment.payment_type,
      paymentMethod: updatedPayment.payment_method,
      description: updatedPayment.description,
      status: updatedPayment.status,
      processedBy: updatedPayment.processed_by,
      paymentDate: updatedPayment.payment_date,
      createdAt: updatedPayment.created_at,
      updatedAt: updatedPayment.updated_at
    };

    return createResponse(responsePayment, true, 'Payment updated successfully');

  } catch (error) {
    console.error('Error updating payment:', error);
    return createErrorResponse('Failed to update payment', 500);
  }
}
